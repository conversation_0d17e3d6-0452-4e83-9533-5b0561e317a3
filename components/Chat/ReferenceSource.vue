<template>
    <Transition name="slide-in" appear>
        <div v-if="openReferenceSource" class="flex flex-col border-l border-gray-200 bg-gray-50">
            <div class="flex items-center justify-between text-base border-b border-gray-200 px-7 ">
                <div class="py-3 text-gray-800 ">参考来源
                    <span class="px-2 py-1 ml-2.5 text-sm bg-gray-100 text-gray-500 rounded-md">{{
                        replyMessageResulsCount
                        }}篇</span>
                </div>
                <button class="p-2 text-gray-300 cursor-pointer hover:text-gray-500" @click="handleClose">
                    <close theme="outline" size="15" />
                </button>
            </div>
            <div ref="scrollContainer" class="flex-1 overflow-auto text-sm text-gray-800 ">
                <div v-for="item in replyMessage" :key="item.id">
                    <div v-if="item.result.results.length">
                        <!-- <template v-if="item.tool_name === AgentToolName.repo_search_filename">
                        <div class="p-3.5 rounded-lg bg-white mx-7 my-3 shadow-sm w-72 cursor-pointer"
                            v-for="resultItem in item.result.results" :key="resultItem.file_id"
                            @click="handleOpenKnowedge(resultItem)">
                            <div class="flex items-center space-x-2">
                                <Iconfont name="wodezhishiku1" :size="15"></Iconfont>
                                <span class="text-sm font-medium ">知识库文件</span>
                            </div>
                            <div class="mt-2.5 ">{{ resultItem.title }}</div>
                            <div class="mt-1 leading-relaxed text-gray-500 line-clamp-3">
                                {{ resultItem.desc }}
                            </div>
                        </div>
                    </template>
<template v-else-if="item.tool_name === AgentToolName.repo_file_block">
                        <div class="p-3.5 rounded-lg bg-white mx-7 my-3 shadow-sm w-72 cursor-pointer"
                            v-for="(resultItem, index) in item.result.results" :key="index"
                            @click="handleOpenKnowedge(resultItem)">
                            <div class="flex items-center space-x-2">
                                <Iconfont name="wodezhishiku1" :size="15"></Iconfont>
                                <span class="text-sm font-medium ">知识库资料</span>
                            </div>
                            <div class="mt-2.5 ">{{ resultItem.title }}</div>
                            <div class="mt-1 leading-relaxed text-gray-500 line-clamp-3">
                                {{ resultItem.desc }}
                            </div>
                        </div>
                    </template> -->
                        <div v-for="(resultItem, index) in item.result.results" :key="index"
                            :class="`p-3.5 rounded-lg  mx-7 my-3 shadow-sm w-72 cursor-pointer ${getAgentToolReferenceBoxClass(item?.result?.type)}`"
                            @click="handleOpenKnowedge(resultItem, item)">
                            <div class="flex items-center space-x-2">
                                <Iconfont :name="getAgentToolReferenceIconName(item?.result?.type)" :size="15">
                                </Iconfont>
                                <span
                                    :class="`text-sm font-medium ${getAgentToolReferenceTitleClass(item?.result?.type)}`">
                                    {{
                                        MessageResultTypeName[item.result.type as keyof typeof MessageResultTypeName] }}
                                </span>
                            </div>
                            <div :class="`mt-2.5 whitespace-nowrap overflow-hidden text-ellipsis`">
                                {{ resultItem.title }}
                            </div>
                            <div class="mt-1 leading-relaxed text-gray-500 line-clamp-3">
                                {{ resultItem.desc }}
                            </div>
                            <div class="flex justify-end pt-3"
                                v-if="[AgentToolName.read_web_url, AgentToolName.web_search, AgentToolName.scholar_search].includes(item.tool_name)">
                                <button @click.stop="handleAddToKnowledge(resultItem, item)"
                                    class="flex items-center justify-center px-3 py-1.5 text-gray-800 bg-white border border-blue-100 rounded-lg cursor-pointer hover:border-blue-150 hover:text-blue-600 ">
                                    <lightning :size="14" /> <span class="pl-1 text-sm ">加入知识库</span>
                                </button>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </Transition>
</template>
<script setup lang="ts">
import Iconfont from '@/components/Iconfont.vue';
import { useChatStore } from '@/stores/chat';
import { Close, Lightning } from '@icon-park/vue-next';
import { cloneDeep } from 'lodash';

import { ref } from 'vue';
import { addHtml } from '~/api/repositoryFile';
import { ToastService } from '~/services/toast';
import { UserService } from '~/services/user';
const sourceList = ref<any[]>([]);
const { replyMessage, openReferenceSource } = storeToRefs(useChatStore());
const scrollContainer = ref<HTMLElement>();
const handleClose = () => {
    openReferenceSource.value = false;
};
// const isOpenWrap = computed({
//     get: () => isOpen.value && replyMessageResulsCount.value > 0,
//     set: (val) => {
//         isOpen.value = val;
//     }
// });
const replyMessageResulsCount = computed(() => {
    let count = 0;
    replyMessage.value.forEach(item => {
        count += item.result.results.length;

    });
    return count;
});
// 监听结果数量变化，自动滚动到顶部
watch(replyMessage, () => {
    nextTick(() => {
        if (scrollContainer.value) {
            scrollContainer.value.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }
    });
});
const getAgentToolReferenceIconName = (type: string) => {
    if (type == MessageResultType.WEB) {
        return 'lianwang'
    }
    if (type == MessageResultType.SCHOLAR) {
        return 'xueshuwenzhang'
    }
    return 'wodezhishiku1'
}
const getAgentToolReferenceTitleClass = (tool_name: string) => {
    if (tool_name == MessageResultType.SCHOLAR) {
        return 'text-blue-600'
    }

    return 'text-gray-800'
}
const getAgentToolReferenceBoxClass = (tool_name: string) => {
    if (tool_name == MessageResultType.SCHOLAR) {
        return 'bg-blue-50 border border-blue-100'
    }
    return 'bg-white border border-white'
}
const handleOpenKnowedge = (_item: any, replyMessageItem: any) => {
    const item = cloneDeep(_item)
    if (replyMessageItem?.result?.type === MessageResultType.WEB) {
        console.log('item', item)
        if (item?.meta?.url) {
            window.open(item?.meta?.url, '_blank')
            return
        }
    }
    if (replyMessageItem?.result?.type === MessageResultType.SCHOLAR) {
        const url = item?.meta?.main_url || item?.meta?.pdf_url || ''
        if (url) {
            window.open(url, '_blank')
            return
        }
    }
    if (!item || !item.file_id) {
        // toast.show('该文件获取失败')
        return
    }

    if (item.meta && item.meta.bboxs) {
        const jsonString = JSON.stringify(item.meta)
        // 编码JSON字符串以确保它可以通过URL传输
        const meta = encodeURIComponent(jsonString)
        return window.open(`${PagePath.MyKnowledgeDetail}/${item.file_id}?meta=${meta}`, '_blank')
    }

    window.open(
        `${PagePath.MyKnowledgeDetail}/${item.file_id}?pageNum=${item.meta?.page || 1}`,
        '_blank'
    )

}
const handleAddToKnowledge = async (_item: any, replyMessageItem: any) => {
    const item = cloneDeep(_item)
    let url = ''
    if (replyMessageItem.tool_name === AgentToolName.web_search) {
        url = item?.meta?.url
    }
    const params = {
        "spaceId": UserService.getSelfUserId(),
        "url": url,
        "fileCategory": addToDefaultFolder.MESSAGE,
    }
    const res = await addHtml(params)
    if (!res.success) {
        return
    }
    ToastService.success('添加成功')
}
</script>
<style scoped>
.slide-in-enter-active,
.slide-in-leave-active {
    transition: all 0.3s ease-out;
}

.slide-in-enter-from {
    transform: translateX(100%);
    opacity: 0;
}

.slide-in-leave-to {
    transform: translateX(100%);
    opacity: 0;
}
</style>