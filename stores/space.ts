import { getSpaceBytes } from '@/api/repositoryFile';

import { useUserStore } from './user';

interface SpaceState {
    spaceUsedBytes: number
    spaceQuotaBytes: number
}

export const useSpaceStore = defineStore('space', {
    state: (): SpaceState => ({
        spaceUsedBytes: 0,
        spaceQuotaBytes: 0,
    }),
    getters: {
        spaceId(): string {
            const userStore = useUserStore()
            return userStore.currentLoginInfo?.id || ''
        }
    },
    actions: {
        async loadSpaceInfo() {
            if (!this.spaceId) {
                return
            }
            const result = await getSpaceBytes(this.spaceId)
            if (!result || !result.ok) {
                return
            }
            this.spaceUsedBytes = result.data?.spaceUsedBytes || 0
            this.spaceQuotaBytes = result.data?.spaceQuotaBytes || 0
        }
    },
})



